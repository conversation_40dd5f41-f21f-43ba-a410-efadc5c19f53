// @ts-check
import { defineConfig, envField } from 'astro/config';

import auth from "auth-astro";

// https://astro.build/config
export default defineConfig({
  output: "server",

  server: {
    allowedHosts: ["paykka-duty.liyujun.dev"]
  },

  env: {
    schema: {
      WECOM_CORP_ID: envField.string({ context: "client", access: "public" }),
      WECOM_CORP_SECRET: envField.string({ context: "server", access: "secret" }),
      WECOM_AGENT_ID: envField.string({ context: "client", access: "public" }),
      // KEEP_API_URL: envField.string({ context: "server", access: "secret" }),
      // KEEP_API_KEY: envField.string({ context: "server", access: "secret" }),
    }
  },

  integrations: [auth()]
});