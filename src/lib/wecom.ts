import { WECOM_AGENT_ID, WECOM_CORP_ID } from "astro:env/client";
import { WECOM_CORP_SECRET } from "astro:env/server";


interface WecomClientOptions {
  corpId: string;
  corpSecret: string;
  agentId: string;
  baseUrl: string;
}

const wecom = (options: WecomClientOptions) => {
  return {
    getAccessToken: async () => {
      const res = await fetch(`${options.baseUrl}/gettoken?corpid=${options.corpId}&corpsecret=${options.corpSecret}`);
      return await res.json();
    }
  }
}

export default wecom({
  corpId: WECOM_CORP_ID,
  corpSecret: WECOM_CORP_SECRET,
  agentId: WECOM_AGENT_ID,
  baseUrl: "https://qyapi.weixin.qq.com/cgi-bin"
});

