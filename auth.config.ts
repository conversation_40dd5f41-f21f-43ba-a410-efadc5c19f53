import { WECOM_AGENT_ID, WECOM_CORP_ID } from "astro:env/client";
import { defineConfig } from "auth-astro";

export default defineConfig({
  providers: [
    {
      id: "wecom",
      name: "WeCom",
      type: "oauth",
      issuer: "https://open.work.weixin.qq.com",
      authorization: {
        url: "https://open.weixin.qq.com/connect/oauth2/authorize#wechat_redirect",
        params: {
          appid: WECOM_CORP_ID,
          agentid: WECOM_AGENT_ID,
          scope: "snsapi_privateinfo",
          response_type: "code"
        }
      },
    }
  ]
})